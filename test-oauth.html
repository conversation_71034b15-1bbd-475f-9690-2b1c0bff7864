<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PlantConnects OAuth Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background-color: #4285f4;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
            display: inline-block;
            text-decoration: none;
        }
        .button:hover {
            background-color: #3367d6;
        }
        .info {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #2196f3;
        }
        .error {
            background-color: #ffebee;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #f44336;
        }
        .success {
            background-color: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #4caf50;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 14px;
        }
        .url-list {
            list-style-type: none;
            padding: 0;
        }
        .url-list li {
            background-color: #f9f9f9;
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 3px;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌱 PlantConnects OAuth Test</h1>
        
        <div class="info">
            <h3>Current Issue</h3>
            <p>The app is redirecting to <code>https://auth.expo.io</code> and showing "Something went wrong trying to finish signing in..." error.</p>
        </div>

        <div class="error">
            <h3>⚠️ Action Required</h3>
            <p>You need to add the following redirect URI to your Google OAuth client configuration in Google Cloud Console:</p>
            <ul class="url-list">
                <li><strong>https://xvgmitjuwgmvllgompsm.supabase.co/auth/v1/callback</strong></li>
            </ul>
            <p>This is the most important one - without it, Google OAuth will fail.</p>
        </div>

        <h3>Test OAuth Flow</h3>
        <p>Click the button below to test the Google OAuth flow directly:</p>
        
        <button class="button" onclick="testOAuth()">🔐 Test Google OAuth</button>
        
        <div id="result"></div>

        <h3>Required Google OAuth Redirect URIs</h3>
        <p>Add these to your Google Cloud Console OAuth client configuration:</p>
        <ul class="url-list">
            <li>https://xvgmitjuwgmvllgompsm.supabase.co/auth/v1/callback</li>
            <li>http://localhost:8081</li>
            <li>https://localhost:8081</li>
            <li>https://auth.expo.io/@geoattract/app-plantconnects-290725</li>
            <li>https://plantconnects.com/auth/callback</li>
        </ul>

        <h3>Steps to Fix</h3>
        <ol>
            <li>Go to <a href="https://console.cloud.google.com/" target="_blank">Google Cloud Console</a></li>
            <li>Navigate to APIs & Services > Credentials</li>
            <li>Find your OAuth 2.0 Client ID: <code>13650106015-j2n38d7kmh8berrj6itd2ef8aidnijt2.apps.googleusercontent.com</code></li>
            <li>Add the redirect URIs listed above</li>
            <li>Save the configuration</li>
            <li>Test the OAuth flow again</li>
        </ol>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

        const supabaseUrl = 'https://xvgmitjuwgmvllgompsm.supabase.co';
        const supabaseKey = 'sb_publishable_qnuSaqkuv49hAmSgLd5weA_WiWxw2bu';
        
        const supabase = createClient(supabaseUrl, supabaseKey);

        window.testOAuth = async function() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info">Testing OAuth flow...</div>';

            try {
                const { data, error } = await supabase.auth.signInWithOAuth({
                    provider: 'google',
                    options: {
                        redirectTo: window.location.origin + '/auth/callback',
                    },
                });

                if (error) {
                    resultDiv.innerHTML = `<div class="error">
                        <h4>❌ OAuth Error</h4>
                        <p>${error.message}</p>
                        <pre>${JSON.stringify(error, null, 2)}</pre>
                    </div>`;
                } else {
                    resultDiv.innerHTML = `<div class="success">
                        <h4>✅ OAuth URL Generated</h4>
                        <p>Redirecting to Google OAuth...</p>
                        <p><strong>URL:</strong> <a href="${data.url}" target="_blank">${data.url}</a></p>
                    </div>`;
                    
                    // Redirect to OAuth URL
                    window.location.href = data.url;
                }
            } catch (err) {
                resultDiv.innerHTML = `<div class="error">
                    <h4>❌ Test Failed</h4>
                    <p>${err.message}</p>
                    <pre>${JSON.stringify(err, null, 2)}</pre>
                </div>`;
            }
        };

        // Handle OAuth callback
        window.addEventListener('load', async () => {
            const urlParams = new URLSearchParams(window.location.search);
            const accessToken = urlParams.get('access_token');
            const refreshToken = urlParams.get('refresh_token');
            const error = urlParams.get('error');

            if (error) {
                document.getElementById('result').innerHTML = `<div class="error">
                    <h4>❌ OAuth Callback Error</h4>
                    <p>${error}</p>
                    <p>${urlParams.get('error_description') || ''}</p>
                </div>`;
            } else if (accessToken && refreshToken) {
                document.getElementById('result').innerHTML = `<div class="success">
                    <h4>✅ OAuth Success!</h4>
                    <p>Successfully received tokens from OAuth callback.</p>
                    <p><strong>Access Token:</strong> ${accessToken.substring(0, 20)}...</p>
                    <p><strong>Refresh Token:</strong> ${refreshToken.substring(0, 20)}...</p>
                </div>`;
            }
        });
    </script>
</body>
</html>
