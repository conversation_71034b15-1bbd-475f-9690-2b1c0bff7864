#!/usr/bin/env node

/**
 * Test script to verify Supabase authentication configuration
 * Run with: node scripts/test-auth.js
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('EXPO_PUBLIC_SUPABASE_URL:', supabaseUrl ? '✅ Set' : '❌ Missing');
  console.log('EXPO_PUBLIC_SUPABASE_KEY:', supabaseKey ? '✅ Set' : '❌ Missing');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testAuth() {
  console.log('🔍 Testing Supabase Authentication Configuration');
  console.log('📍 Supabase URL:', supabaseUrl);
  console.log('🔑 Supabase Key:', supabaseKey.substring(0, 20) + '...');
  
  try {
    // Test basic connection
    console.log('\n1. Testing basic connection...');
    const { data, error } = await supabase.auth.getSession();
    if (error) {
      console.error('❌ Connection error:', error.message);
    } else {
      console.log('✅ Connection successful');
    }

    // Test Google OAuth URL generation
    console.log('\n2. Testing Google OAuth URL generation...');
    const redirectUri = 'plantconnects://auth/callback';
    console.log('📍 Redirect URI:', redirectUri);
    
    const { data: oauthData, error: oauthError } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: redirectUri,
        skipBrowserRedirect: true,
      },
    });

    if (oauthError) {
      console.error('❌ OAuth URL generation error:', oauthError.message);
    } else {
      console.log('✅ OAuth URL generated successfully');
      console.log('🔗 OAuth URL:', oauthData.url);
      
      // Check if the URL contains the correct redirect URI
      if (oauthData.url && oauthData.url.includes(encodeURIComponent(redirectUri))) {
        console.log('✅ Redirect URI correctly included in OAuth URL');
      } else {
        console.log('⚠️  Redirect URI may not be correctly included in OAuth URL');
      }
    }

    // Test different redirect URI formats
    console.log('\n3. Testing different redirect URI formats...');
    const testUris = [
      'plantconnects://auth/callback',
      'https://auth.expo.io/@geoattract/app-plantconnects-290725',
      'exp://*************:8081/--/auth/callback',
      'https://plantconnects.com/auth/callback'
    ];

    for (const uri of testUris) {
      try {
        const { data: testData, error: testError } = await supabase.auth.signInWithOAuth({
          provider: 'google',
          options: {
            redirectTo: uri,
            skipBrowserRedirect: true,
          },
        });

        if (testError) {
          console.log(`❌ ${uri}: ${testError.message}`);
        } else {
          console.log(`✅ ${uri}: URL generated successfully`);
        }
      } catch (err) {
        console.log(`❌ ${uri}: ${err.message}`);
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testAuth().then(() => {
  console.log('\n🏁 Test completed');
}).catch((error) => {
  console.error('❌ Test script error:', error);
});
