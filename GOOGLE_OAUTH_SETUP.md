# Google OAuth Setup for PlantConnects App

## Current Issue
The app is redirecting to `https://auth.expo.io` and showing "Something went wrong trying to finish signing in..." error.

## Root Cause
The Google OAuth client configuration in Google Cloud Console needs to have the correct redirect URIs configured.

## Required Redirect URIs in Google Cloud Console

You need to add these redirect URIs to your Google OAuth client configuration:

### 1. Supabase Auth Callback (Primary)
```
https://xvgmitjuwgmvllgompsm.supabase.co/auth/v1/callback
```

### 2. Development URLs (for testing)
```
http://localhost:8081
https://localhost:8081
```

### 3. Expo Development URLs
```
https://auth.expo.io/@geoattract/app-plantconnects-290725
```

### 4. Production URLs (when deployed)
```
https://plantconnects.com/auth/callback
```

## Steps to Fix

1. **Go to Google Cloud Console**
   - Navigate to: https://console.cloud.google.com/
   - Select your project

2. **Go to OAuth 2.0 Client IDs**
   - Navigate to: APIs & Services > Credentials
   - Find your OAuth 2.0 Client ID: `13650106015-j2n38d7kmh8berrj6itd2ef8aidnijt2.apps.googleusercontent.com`

3. **Add Redirect URIs**
   - Click on your OAuth client
   - In the "Authorized redirect URIs" section, add:
     - `https://xvgmitjuwgmvllgompsm.supabase.co/auth/v1/callback` (MOST IMPORTANT)
     - `http://localhost:8081`
     - `https://localhost:8081`
     - `https://auth.expo.io/@geoattract/app-plantconnects-290725`
     - `https://plantconnects.com/auth/callback`

4. **Save the configuration**

## Why This Fixes the Issue

- Google OAuth requires exact redirect URI matches
- When Supabase handles OAuth, it redirects to its own callback URL first
- Supabase then processes the tokens and redirects to your app
- Without the correct Supabase callback URL in Google's configuration, the OAuth flow fails

## Testing After Fix

1. Clear your app's cache/data
2. Try the Google sign-in again
3. The flow should now work correctly

## Current Supabase Configuration

✅ Site URL: `https://plantconnects.com`
✅ Allowed redirect URLs: 
- `https://plantconnects.com`
- `https://auth.expo.io/@geoattract/app-plantconnects-290725`
- `plantconnects://auth/callback`
- `exp://*.exp.direct`
- `http://localhost:*`
- `exp://192.168.*:*`
- `exp://10.*:*`
- `exp://172.*:*`
- `https://localhost:*`

The Supabase side is correctly configured. The issue is on the Google OAuth client side.
